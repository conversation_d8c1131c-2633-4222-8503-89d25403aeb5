/**
 * Chat Service
 * Handles chat API communication and message management
 */

import { http } from './baseHttp';

// Types
export interface ToolUsed {
  name: string;
  description?: string;
  input?: any;
  output?: any;
}

export interface ChatMessage {
  id: string;
  message: string;
  response: string;
  timestamp: Date;
  thread_id: string;
  user_id: string;
  tools_used?: ToolUsed[];
}

export interface ChatRequest {
  message: string;
}

export interface ChatResponse {
  response: string;
  thread_id: string;
  user_id: string;
  message_id: string;
  tools_used?: ToolUsed[];
}

export interface ChatHistoryMessage {
  type: 'human' | 'ai';
  content: string;
  timestamp?: Date;
}

export interface ChatHistoryResponse {
  session_id: string;
  messages: ChatHistoryMessage[];
  message_count: number;
}

export interface ClearHistoryResponse {
  success: boolean;
  message: string;
}

/**
 * Chat Service Class
 */
class ChatService {
  private readonly CHAT_HISTORY_KEY = 'chat_history';

  /**
   * Send a chat message to the API
   */
  async sendMessage(message: string): Promise<ChatResponse> {
    try {
      const response = await http.post<ChatResponse>('/api/v1/chat', {
        message,
      });

      // Store message in local history
      this.addToHistory({
        id: response.data.message_id || Date.now().toString(),
        message,
        response: response.data.response,
        timestamp: new Date(),
        thread_id: response.data.thread_id,
        user_id: response.data.user_id,
        tools_used: response.data.tools_used || [],
      });

      return response.data;
    } catch (error: unknown) {
      const axiosError = error as { response?: { data?: { detail?: string } } };
      throw new Error(axiosError.response?.data?.detail || 'Failed to send message');
    }
  }

  /**
   * Get chat history from local storage
   */
  getChatHistory(): ChatMessage[] {
    const historyStr = localStorage.getItem(this.CHAT_HISTORY_KEY);
    if (!historyStr) return [];

    try {
      const history = JSON.parse(historyStr);
      return history.map((msg: { id: string; message: string; response: string; timestamp: string; user_id?: string }) => ({
        ...msg,
        timestamp: new Date(msg.timestamp),
      }));
    } catch {
      return [];
    }
  }

  /**
   * Add message to chat history
   */
  private addToHistory(message: ChatMessage): void {
    const history = this.getChatHistory();
    history.push(message);

    // Keep only last 100 messages
    if (history.length > 100) {
      history.splice(0, history.length - 100);
    }

    localStorage.setItem(this.CHAT_HISTORY_KEY, JSON.stringify(history));
  }

  /**
   * Get chat history from server using MongoDBChatMessageHistory
   */
  async getServerChatHistory(userId?: string, limit?: number): Promise<ChatHistoryResponse> {
    try {
      const params = new URLSearchParams();
      if (userId) params.append('user_id', userId);
      if (limit) params.append('limit', limit.toString());

      const response = await http.get<ChatHistoryResponse>(`/api/v1/history?${params.toString()}`);
      return response.data;
    } catch (error: unknown) {
      const axiosError = error as { response?: { data?: { detail?: string } } };
      throw new Error(axiosError.response?.data?.detail || 'Failed to get chat history');
    }
  }

  /**
   * Clear chat history from both local storage and server
   */
  async clearHistory(): Promise<void> {
    try {
      // Clear server-side conversation memory
      await http.delete('/api/v1/chat/clear');

      // Clear local storage
      localStorage.removeItem(this.CHAT_HISTORY_KEY);
    } catch (error: unknown) {
      const axiosError = error as { response?: { data?: { detail?: string } } };
      console.error('Failed to clear conversation:', axiosError.response?.data?.detail || 'Unknown error');

      // Still clear local storage even if server call fails
      localStorage.removeItem(this.CHAT_HISTORY_KEY);

      throw new Error(axiosError.response?.data?.detail || 'Failed to clear conversation');
    }
  }

  /**
   * Clear server chat history using new API
   */
  async clearServerChatHistory(userId?: string): Promise<ClearHistoryResponse> {
    try {
      const params = new URLSearchParams();
      if (userId) params.append('user_id', userId);

      const response = await http.delete<ClearHistoryResponse>(`/api/v1/history?${params.toString()}`);
      return response.data;
    } catch (error: unknown) {
      const axiosError = error as { response?: { data?: { detail?: string } } };
      throw new Error(axiosError.response?.data?.detail || 'Failed to clear chat history');
    }
  }

  /**
   * Clear all chat history for current user
   */
  async clearAllChatHistory(): Promise<ClearHistoryResponse> {
    try {
      const response = await http.post<ClearHistoryResponse>('/api/v1/clear-all');

      // Also clear local storage
      localStorage.removeItem(this.CHAT_HISTORY_KEY);

      return response.data;
    } catch (error: unknown) {
      const axiosError = error as { response?: { data?: { detail?: string } } };
      throw new Error(axiosError.response?.data?.detail || 'Failed to clear all chat history');
    }
  }

  /**
   * Get all chat sessions
   */
  async getChatSessions(): Promise<any> {
    try {
      const response = await http.get('/api/v1/sessions');
      return response.data;
    } catch (error: unknown) {
      const axiosError = error as { response?: { data?: { detail?: string } } };
      throw new Error(axiosError.response?.data?.detail || 'Failed to get chat sessions');
    }
  }

  /**
   * Check chat service health
   */
  async checkHealth(): Promise<boolean> {
    try {
      await http.get('/api/v1/chat/health');
      return true;
    } catch {
      return false;
    }
  }
}

// Export singleton instance
export const chatService = new ChatService();
export default chatService;
