@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom animations for chat interface */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.animate-fadeIn {
  animation: fadeIn 0.5s ease-out;
}

.animate-slideInLeft {
  animation: slideInLeft 0.6s ease-out;
}

.animate-slideInRight {
  animation: slideInRight 0.6s ease-out;
}

.animate-scaleIn {
  animation: scaleIn 0.4s ease-out;
}

.animate-shimmer {
  animation: shimmer 2s infinite linear;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  background-size: 200px 100%;
}

/* Smooth scrolling for chat messages */
.chat-scroll {
  scroll-behavior: smooth;
}

/* Custom scrollbar for chat area */
.chat-scroll::-webkit-scrollbar {
  width: 6px;
}

.chat-scroll::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.chat-scroll::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.chat-scroll::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Prose styling for formatted AI messages */
.prose {
  max-width: none;
}

.prose p {
  margin-bottom: 0.5rem;
}

.prose ul {
  margin: 0.5rem 0;
  padding-left: 1rem;
}

.prose li {
  margin-bottom: 0.25rem;
}

@layer components {
  /* Custom component styles */
  .chat-bubble {
    @apply max-w-xs lg:max-w-md px-4 py-2 rounded-2xl relative group;
  }

  .chat-bubble-user {
    @apply bg-blue-600 text-white rounded-br-md;
  }

  .chat-bubble-bot {
    @apply bg-white border border-gray-200 rounded-bl-md;
  }

  .gradient-text {
    @apply bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent;
  }
}

/* Base styles */
* {
  border-color: #e5e7eb;
}

body {
  background-color: white;
  color: #111827;
  font-feature-settings: "rlig" 1, "calt" 1;
  margin: 0;
  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
}

@layer components {
  /* Custom component styles */
  .chat-bubble {
    @apply max-w-xs lg:max-w-md px-4 py-2 rounded-2xl relative group;
  }

  .chat-bubble-user {
    @apply bg-blue-600 text-white rounded-br-md;
  }

  .chat-bubble-bot {
    @apply bg-white text-gray-800 shadow-md border rounded-bl-md;
  }
}

@layer utilities {
  /* Custom utility classes */
  .text-balance {
    text-wrap: balance;
  }

  .animate-typing {
    animation: typing 1.5s infinite;
  }

  @keyframes typing {
    0%, 100% { opacity: 0.5; }
    50% { opacity: 1; }
  }
}
