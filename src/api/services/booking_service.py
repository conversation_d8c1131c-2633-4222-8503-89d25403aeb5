import logging
from datetime import datetime
from typing import List, Optional
from bson import ObjectId
from pymongo.database import Database

from models.booking import BookingModel, BookingCreate, BookingResponse
from core.database import get_db_from_tenant_id

logger = logging.getLogger(__name__)


class BookingService:
    """Service for managing course bookings"""
    
    def __init__(self, tenant_id: str):
        self.tenant_id = tenant_id
        self.db = get_db_from_tenant_id(tenant_id)
        self.collection = self.db.bookings
        
    async def create_booking(
        self, 
        booking_data: BookingCreate, 
        user_id: str, 
        thread_id: str
    ) -> BookingResponse:
        """Create a new booking"""
        try:
            # Create booking document
            booking_doc = {
                "user_name": booking_data.user_name,
                "user_email": booking_data.user_email,
                "user_phone": booking_data.user_phone,
                "course_name": booking_data.course_name,
                "course_code": booking_data.course_code,
                "time_slot": booking_data.time_slot,
                "booking_date": datetime.now(),
                "status": booking_data.status,
                "tenant_id": self.tenant_id,
                "user_id": user_id,
                "thread_id": thread_id
            }
            
            # Insert into database
            result = self.collection.insert_one(booking_doc)
            booking_id = str(result.inserted_id)
            
            logger.info(f"✅ Booking created with ID: {booking_id}")
            
            return BookingResponse(
                booking_id=booking_id,
                user_name=booking_data.user_name,
                course_name=booking_data.course_name,
                course_code=booking_data.course_code,
                time_slot=booking_data.time_slot,
                booking_date=booking_doc["booking_date"],
                status=booking_data.status
            )
            
        except Exception as e:
            logger.error(f"❌ Error creating booking: {e}")
            raise
    
    async def get_user_bookings(self, user_id: str) -> List[BookingResponse]:
        """Get all bookings for a user"""
        try:
            bookings = list(self.collection.find({"user_id": user_id}))
            
            return [
                BookingResponse(
                    booking_id=str(booking["_id"]),
                    user_name=booking["user_name"],
                    course_name=booking["course_name"],
                    course_code=booking["course_code"],
                    time_slot=booking["time_slot"],
                    booking_date=booking["booking_date"],
                    status=booking["status"]
                )
                for booking in bookings
            ]
            
        except Exception as e:
            logger.error(f"❌ Error getting user bookings: {e}")
            return []
    
    async def get_booking_by_id(self, booking_id: str) -> Optional[BookingResponse]:
        """Get a specific booking by ID"""
        try:
            booking = self.collection.find_one({"_id": ObjectId(booking_id)})
            
            if booking:
                return BookingResponse(
                    booking_id=str(booking["_id"]),
                    user_name=booking["user_name"],
                    course_name=booking["course_name"],
                    course_code=booking["course_code"],
                    time_slot=booking["time_slot"],
                    booking_date=booking["booking_date"],
                    status=booking["status"]
                )
            return None
            
        except Exception as e:
            logger.error(f"❌ Error getting booking: {e}")
            return None

    async def update_booking_status(self, booking_id: str, status: str) -> bool:
        """Update booking status"""
        try:
            result = self.collection.update_one(
                {"_id": ObjectId(booking_id)},
                {"$set": {"status": status, "updated_at": datetime.now()}}
            )

            if result.modified_count > 0:
                logger.info(f"✅ Booking {booking_id} status updated to {status}")
                return True
            else:
                logger.warning(f"⚠️ No booking found with ID: {booking_id}")
                return False

        except Exception as e:
            logger.error(f"❌ Error updating booking status: {e}")
            return False

    async def cancel_booking(self, booking_id: str) -> bool:
        """Cancel a booking"""
        return await self.update_booking_status(booking_id, "cancelled")

    async def confirm_booking(self, booking_id: str) -> bool:
        """Confirm a booking"""
        return await self.update_booking_status(booking_id, "confirmed")

    async def delete_booking(self, booking_id: str) -> bool:
        """Delete a booking permanently"""
        try:
            result = self.collection.delete_one({"_id": ObjectId(booking_id)})

            if result.deleted_count > 0:
                logger.info(f"✅ Booking {booking_id} deleted permanently")
                return True
            else:
                logger.warning(f"⚠️ No booking found with ID: {booking_id}")
                return False

        except Exception as e:
            logger.error(f"❌ Error deleting booking: {e}")
            return False

    async def get_user_booking_history(self, user_id: str, limit: int = 50) -> List[BookingResponse]:
        """Get booking history for a user"""
        try:
            cursor = self.collection.find(
                {"user_id": user_id, "tenant_id": self.tenant_id}
            ).sort("booking_date", -1).limit(limit)

            bookings = []
            for booking in cursor:
                bookings.append(BookingResponse(
                    booking_id=str(booking["_id"]),
                    user_name=booking["user_name"],
                    course_name=booking["course_name"],
                    course_code=booking["course_code"],
                    time_slot=booking["time_slot"],
                    booking_date=booking["booking_date"],
                    status=booking["status"]
                ))

            logger.info(f"📋 Retrieved {len(bookings)} bookings for user {user_id}")
            return bookings

        except Exception as e:
            logger.error(f"❌ Error getting user booking history: {e}")
            return []

    async def get_bookings_by_status(self, status: str, limit: int = 100) -> List[BookingResponse]:
        """Get bookings by status"""
        try:
            cursor = self.collection.find(
                {"status": status, "tenant_id": self.tenant_id}
            ).sort("booking_date", -1).limit(limit)

            bookings = []
            for booking in cursor:
                bookings.append(BookingResponse(
                    booking_id=str(booking["_id"]),
                    user_name=booking["user_name"],
                    course_name=booking["course_name"],
                    course_code=booking["course_code"],
                    time_slot=booking["time_slot"],
                    booking_date=booking["booking_date"],
                    status=booking["status"]
                ))

            logger.info(f"📋 Retrieved {len(bookings)} bookings with status: {status}")
            return bookings

        except Exception as e:
            logger.error(f"❌ Error getting bookings by status: {e}")
            return []

    async def get_booking_stats(self) -> Dict[str, Any]:
        """Get booking statistics"""
        try:
            pipeline = [
                {"$match": {"tenant_id": self.tenant_id}},
                {"$group": {
                    "_id": "$status",
                    "count": {"$sum": 1}
                }}
            ]

            status_counts = {}
            for result in self.collection.aggregate(pipeline):
                status_counts[result["_id"]] = result["count"]

            total_bookings = sum(status_counts.values())

            # Get recent bookings count (last 7 days)
            week_ago = datetime.now() - timedelta(days=7)
            recent_count = self.collection.count_documents({
                "tenant_id": self.tenant_id,
                "booking_date": {"$gte": week_ago}
            })

            return {
                "total_bookings": total_bookings,
                "status_breakdown": status_counts,
                "recent_bookings_7_days": recent_count
            }

        except Exception as e:
            logger.error(f"❌ Error getting booking stats: {e}")
            return {
                "total_bookings": 0,
                "status_breakdown": {},
                "recent_bookings_7_days": 0
            }
    
    async def cancel_booking(self, booking_id: str, user_id: str) -> bool:
        """Cancel a booking"""
        try:
            result = self.collection.update_one(
                {"_id": ObjectId(booking_id), "user_id": user_id},
                {"$set": {"status": "cancelled"}}
            )
            
            if result.modified_count > 0:
                logger.info(f"✅ Booking {booking_id} cancelled")
                return True
            return False
            
        except Exception as e:
            logger.error(f"❌ Error cancelling booking: {e}")
            return False
