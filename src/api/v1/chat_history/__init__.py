from fastapi import APIRouter, HTTPException, Depends, Query
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
from datetime import datetime

from core.security import get_tenant_info
from models.user import UserTenantDB
from utils.production_memory_manager import get_production_memory_manager
from utils import setup_colored_logging, log_info, log_error, log_success

# Setup logging
setup_colored_logging()

router = APIRouter(tags=["Chat History"])


class ChatMessage(BaseModel):
    type: str  # "human" or "ai"
    content: str
    timestamp: Optional[datetime] = None


class ChatHistoryResponse(BaseModel):
    session_id: str
    messages: List[ChatMessage]
    message_count: int


class ChatSessionInfo(BaseModel):
    session_id: str
    message_count: int
    last_message: Optional[datetime]
    first_message: Optional[datetime]


class ChatSessionsResponse(BaseModel):
    sessions: List[ChatSessionInfo]
    total_sessions: int


class ClearHistoryResponse(BaseModel):
    success: bool
    message: str


@router.get("/history", response_model=ChatHistoryResponse)
async def get_chat_history(
    session_id: Optional[str] = Query(None, description="Session ID (defaults to user ID)"),
    limit: Optional[int] = Query(50, description="Maximum number of messages to return"),
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    """
    Get chat history for a session
    """
    try:
        # Use user ID as session ID if not provided
        if session_id is None:
            session_id = str(current_user.user.id)
        
        # Get production memory manager
        memory_manager = get_production_memory_manager(current_user.tenant_id)
        
        # Get chat messages
        messages = memory_manager.get_chat_messages(session_id, limit)
        
        # Convert to response format
        chat_messages = []
        for msg in messages:
            msg_type = "human" if hasattr(msg, 'type') and msg.type == "human" else "ai"
            chat_messages.append(ChatMessage(
                type=msg_type,
                content=msg.content,
                timestamp=getattr(msg, 'timestamp', None)
            ))
        
        log_info(f"Retrieved {len(chat_messages)} messages for session {session_id}")
        
        return ChatHistoryResponse(
            session_id=session_id,
            messages=chat_messages,
            message_count=len(chat_messages)
        )
        
    except Exception as e:
        log_error(f"Error getting chat history: {e}")
        raise HTTPException(status_code=500, detail=f"Error retrieving chat history: {str(e)}")


@router.get("/sessions", response_model=ChatSessionsResponse)
async def get_chat_sessions(
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    """
    Get all chat sessions for the current user
    """
    try:
        # Get production memory manager
        memory_manager = get_production_memory_manager(current_user.tenant_id)
        
        # Get all chat sessions
        sessions_data = memory_manager.get_all_chat_sessions()
        
        # Convert to response format
        sessions = []
        for session in sessions_data:
            sessions.append(ChatSessionInfo(
                session_id=session["_id"],
                message_count=session.get("message_count", 0),
                last_message=session.get("last_message"),
                first_message=session.get("first_message")
            ))
        
        log_info(f"Retrieved {len(sessions)} chat sessions")
        
        return ChatSessionsResponse(
            sessions=sessions,
            total_sessions=len(sessions)
        )
        
    except Exception as e:
        log_error(f"Error getting chat sessions: {e}")
        raise HTTPException(status_code=500, detail=f"Error retrieving chat sessions: {str(e)}")


@router.delete("/history", response_model=ClearHistoryResponse)
async def clear_chat_history(
    session_id: Optional[str] = Query(None, description="Session ID (defaults to user ID)"),
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    """
    Clear chat history for a session
    """
    try:
        # Use user ID as session ID if not provided
        if session_id is None:
            session_id = str(current_user.user.id)
        
        # Get production memory manager
        memory_manager = get_production_memory_manager(current_user.tenant_id)
        
        # Clear chat history
        success = memory_manager.clear_chat_history(session_id)
        
        if success:
            log_success(f"Cleared chat history for session {session_id}")
            return ClearHistoryResponse(
                success=True,
                message=f"Chat history cleared for session {session_id}"
            )
        else:
            return ClearHistoryResponse(
                success=False,
                message=f"Failed to clear chat history for session {session_id}"
            )
        
    except Exception as e:
        log_error(f"Error clearing chat history: {e}")
        raise HTTPException(status_code=500, detail=f"Error clearing chat history: {str(e)}")


@router.delete("/sessions/{session_id}", response_model=ClearHistoryResponse)
async def delete_chat_session(
    session_id: str,
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    """
    Delete entire chat session
    """
    try:
        # Get production memory manager
        memory_manager = get_production_memory_manager(current_user.tenant_id)
        
        # Delete chat session
        success = memory_manager.delete_chat_session(session_id)
        
        if success:
            log_success(f"Deleted chat session {session_id}")
            return ClearHistoryResponse(
                success=True,
                message=f"Chat session {session_id} deleted successfully"
            )
        else:
            return ClearHistoryResponse(
                success=False,
                message=f"Failed to delete chat session {session_id}"
            )
        
    except Exception as e:
        log_error(f"Error deleting chat session: {e}")
        raise HTTPException(status_code=500, detail=f"Error deleting chat session: {str(e)}")


@router.post("/clear-all", response_model=ClearHistoryResponse)
async def clear_all_chat_history(
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    """
    Clear all chat history for the current user
    """
    try:
        user_id = str(current_user.user.id)
        
        # Get production memory manager
        memory_manager = get_production_memory_manager(current_user.tenant_id)
        
        # Clear all chat history for user
        success = memory_manager.clear_chat_history(user_id)
        
        if success:
            log_success(f"Cleared all chat history for user {user_id}")
            return ClearHistoryResponse(
                success=True,
                message="All chat history cleared successfully"
            )
        else:
            return ClearHistoryResponse(
                success=False,
                message="Failed to clear all chat history"
            )
        
    except Exception as e:
        log_error(f"Error clearing all chat history: {e}")
        raise HTTPException(status_code=500, detail=f"Error clearing all chat history: {str(e)}")
